#include "main.h"
#include "scheduler.h"
#include "led_app.h"
#include "uart_app.h"
#include "stdio.h"
#include "string.h"
#include "stdarg.h"
#include "usart.h"
#include "waveform_analyzer_app.h"
#include "arm_math.h"
#include "lcd.h"
#include "delay.h"
#include "tlc5615.h" 


extern uint16_t uart_rx_index;
extern uint32_t uart_rx_ticks;
extern uint8_t uart_rx_buffer[128];
extern uint8_t uart_rx_dma_buffer[128];
extern UART_HandleTypeDef huart1;
extern DMA_HandleTypeDef hdma_usart1_rx;

