#ifndef __DELAY_H
#define __DELAY_H

#include "mydefine.h"

// 定义是否支持OS，0表示不支持OS
#ifndef SYS_SUPPORT_OS
#define SYS_SUPPORT_OS  0
#endif


void delay_init(uint16_t sysclk);           /* ��ʼ���ӳٺ��� */
void delay_ms(uint16_t nms);                /* ��ʱnms */
void delay_us(uint32_t nus);                /* ��ʱnus */

#if (!SYS_SUPPORT_OS)                       /* û��ʹ��Systick�ж� */
    void HAL_Delay(uint32_t Delay);         /* HAL�����ʱ������SDIO����Ҫ�õ� */
#endif

#endif

