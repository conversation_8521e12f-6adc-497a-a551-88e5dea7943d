#include "tlc5615.h" 

/*TLC5615��SPIͨ��ģʽΪMode0 : CPOL = 0, CPHA = 0*/

/*
*********************************************************************************************************
*	函 数 名: tlc5615_Init
*	功能说明: 初始化TLC5615 DAC
*	形    参: 无
*	返 回 值: 无
*********************************************************************************************************
*/
void tlc5615_Init(void)
{
	// 确保SPI引脚初始状态正确
	SCK_0();	// 时钟初始为低电平
	DI_0();     // 数据线初始为低电平
	CS_1();		// 片选初始为高电平（未选中状态）

	// 发送一个空数据，确保DAC处于已知状态
	tlc5615_Write2Byte(0);
}

/*
*********************************************************************************************************
*	�� �� ��: tlc5615_Send8Bit
*	����˵��: ��SPI���߷���8��bit���ݡ� ����CS���ơ�
*	��    ��: _data : ����
*	�� �� ֵ: ��
*********************************************************************************************************
*/ 
void tlc5615_Send8Bit(uint8_t _data)
{
	uint8_t i; 
	
	for(i = 0; i < 8; i++)
	{
		if (_data & 0x80)
		{
			DI_1();
		}
		else
		{
			DI_0();
		}
		SCK_1();
		_data <<= 1;
		delay_us(1);
		SCK_0();	
		delay_us(1);		
	}
}

/*
*********************************************************************************************************
*	�� �� ��: tlc5615_WriteByte
*	����˵��: д��1���ֽڡ���CS����
*	��    ��: _data ����Ҫд�������
*	�� �� ֵ: ��
*********************************************************************************************************
*/
void tlc5615_WriteByte(uint8_t _data)
{
	CS_0();
	tlc5615_Send8Bit(_data);
	CS_1();
}

/*
*********************************************************************************************************
*	�� �� ��: tlc5615_Write2Byte
*	����˵��: д��2���ֽڡ���CS����
*	��    ��: _data ����Ҫд�������
*	�� �� ֵ: ��
*********************************************************************************************************
*/
void tlc5615_Write2Byte(uint16_t _data)
{
	CS_0(); 
	tlc5615_Send8Bit((_data >> 8) & 0xFF);
	tlc5615_Send8Bit(_data);
	CS_1();
} 

/*
*********************************************************************************************************
*	函 数 名: tlc5615_Send12Bit
*	功能说明: 向SPI总线发送12位bit数据。含CS控制。
*	形    参: _data : 数据，范围 0 ~ 1023 (10位DAC)
*	返 回 值: 无
*	说    明: TLC5615是10位DAC，数据需要左移2位对齐到12位格式
*********************************************************************************************************
*/
void tlc5615_Send12Bit(uint16_t _data)
{
	uint8_t i;

	// 限制数据范围到10位 (0-1023)
	if(_data > 1023) _data = 1023;

	// TLC5615需要12位数据，10位数据左移2位对齐
	_data = _data << 2;		// 左移2位，将10位数据对齐到12位格式

	CS_0();

	for(i = 0; i < 12; i++)
	{
		if (_data & 0x0800)  // 检查第11位 (12位数据的最高位)
		{
			DI_1();
		}
		else
		{
			DI_0();
		}
		SCK_1();
		_data <<= 1;
		delay_us(1);
		SCK_0();
		delay_us(1);
	}

	CS_1();
}

/*
*********************************************************************************************************
*	函 数 名: tlc5615_SetVoltage
*	功能说明: 设置TLC5615输出电压
*	形    参: voltage : 输出电压，范围 0.0V ~ 2.0V
*	返 回 值: 无
*********************************************************************************************************
*/
void tlc5615_SetVoltage(float voltage)
{
	uint16_t dac_value;

	// 限制电压范围
	if(voltage < 0.0f) voltage = 0.0f;
	if(voltage > 2.0f) voltage = 2.0f;

	// 计算DAC数值：电压/参考电压 * 满量程
	dac_value = (uint16_t)((voltage / 2.0f) * 1023.0f);

	// 发送数据：10位数据左移6位对齐到16位高位
	tlc5615_Write2Byte(dac_value << 6);
}

/*
*********************************************************************************************************
*	函 数 名: test_TLC5615_Output
*	功能说明: 测试TLC5615固定电压输出
*	形    参: 无
*	返 回 值: 无
*********************************************************************************************************
*/
void test_TLC5615_Output(void)
{
	// 测试不同电压输出，每个持续2秒
	tlc5615_SetVoltage(0.0f);   // 输出0V
	delay_ms(2000);

	tlc5615_SetVoltage(0.5f);   // 输出0.5V
	delay_ms(2000);

	tlc5615_SetVoltage(1.0f);   // 输出1V
	delay_ms(2000);

	tlc5615_SetVoltage(1.5f);   // 输出1.5V
	delay_ms(2000);

	tlc5615_SetVoltage(2.0f);   // 输出2V
	delay_ms(2000);
}

/*
*********************************************************************************************************
*	函 数 名: debug_GPIO_Status
*	功能说明: 调试GPIO引脚状态
*	形    参: 无
*	返 回 值: 无
*********************************************************************************************************
*/
void debug_GPIO_Status(void)
{
	// 测试CS引脚控制
	CS_1(); delay_ms(500);  // CS高电平
	CS_0(); delay_ms(500);  // CS低电平
	CS_1(); delay_ms(500);  // 恢复高电平

	// 测试时钟引脚
	SCK_1(); delay_ms(500); // SCK高电平
	SCK_0(); delay_ms(500); // SCK低电平

	// 测试数据引脚
	DI_1(); delay_ms(500);  // DI高电平
	DI_0(); delay_ms(500);  // DI低电平
}

void generate_Wave(void)
{
	uint16_t n=0, j=0;
	uint16_t wave_low = 0;      // 低电平：0V
	uint16_t wave_high = 1023;  // 高电平：2V (满量程)

	while(1)
	{
		// 生成方波：0V到2V
		for(n=0; n<10; n++)
		{
		   // 使用正确的数据格式：10位数据左移6位
		   tlc5615_Write2Byte(wave_low << 6);   // 输出0V
		   delay_ms(10);
		   tlc5615_Write2Byte(wave_high << 6);  // 输出2V
		   delay_ms(10);
		}

		// 生成三角波：0V到2V
		for(n=0; n<10; n++)
		{
			// 上升沿：从0V到2V
			for(j=0; j<=1023; j+=8)  // 步进8，约128步
			{
			   tlc5615_Write2Byte(j << 6);
			   delay_ms(1);  // 减少延时以提高频率
			}
			// 下降沿：从2V到0V
			for(j=1023; j>0; j-=8)
			{
				tlc5615_Write2Byte(j << 6);
				delay_ms(1);
			}
		}
	}
}







