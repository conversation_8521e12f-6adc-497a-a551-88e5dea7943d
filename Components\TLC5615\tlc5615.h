#ifndef __TLC5615_H__
#define __TLC5615_H__

#include "mydefine.h"


/* 定义GPIO端口 */
#define SCK_GPIO_Port	GPIOD
#define SCK_Pin	 GPIO_PIN_2

#define DIN_GPIO_Port	GPIOD
#define DIN_Pin		GPIO_PIN_3

#define DOUT_GPIO_Port	GPIOD
#define DOUT_Pin	GPIO_PIN_4

#define CS_GPIO_Port		GPIOD
#define CS_Pin		GPIO_PIN_5 


/* 引脚操作宏 */
#define CS_0()  HAL_GPIO_WritePin(CS_GPIO_Port, CS_Pin, GPIO_PIN_RESET)
#define CS_1()  HAL_GPIO_WritePin(CS_GPIO_Port, CS_Pin, GPIO_PIN_SET)

#define SCK_0() HAL_GPIO_WritePin(SCK_GPIO_Port, SCK_Pin, GPIO_PIN_RESET)
#define SCK_1() HAL_GPIO_WritePin(SCK_GPIO_Port, SCK_Pin, GPIO_PIN_SET)

#define DI_0()  HAL_GPIO_WritePin(DIN_GPIO_Port, DIN_Pin, GPIO_PIN_RESET)
#define DI_1()  HAL_GPIO_WritePin(DIN_GPIO_Port, DIN_Pin, GPIO_PIN_SET)

#define DO_IS_HIGH() (HAL_GPIO_ReadPin(DOUT_GPIO_Port, DOUT_Pin) == GPIO_PIN_SET)




void tlc5615_Init(void);
void tlc5615_Send8Bit(uint8_t _data);
void tlc5615_WriteByte(uint8_t _data);
void tlc5615_Write2Byte(uint16_t _data);
void tlc5615_Send12Bit(uint16_t _data);
void generate_Wave(void);



#endif
 









































